#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复文件编码问题
"""

def fix_file_encoding(input_file, output_file):
    """修复文件编码"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig', 'latin1']
    
    content = None
    for encoding in encodings:
        try:
            with open(input_file, 'r', encoding=encoding) as f:
                content = f.read()
            print(f"✅ 成功用 {encoding} 编码读取文件")
            break
        except UnicodeDecodeError:
            print(f"❌ {encoding} 编码失败")
            continue
        except Exception as e:
            print(f"❌ {encoding} 读取出错: {e}")
            continue
    
    if content is None:
        print("❌ 无法读取文件")
        return False
    
    # 用UTF-8重新保存
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 文件已用UTF-8编码保存为: {output_file}")
        return True
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        return False

if __name__ == "__main__":
    input_file = "熵易项目针对性评委提问100题.md"
    output_file = "熵易项目204题完整答辩资料.md"
    
    print("🔧 开始修复文件编码...")
    if fix_file_encoding(input_file, output_file):
        print("🎉 编码修复完成！")
    else:
        print("💥 编码修复失败！")
