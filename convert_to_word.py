#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将熵易项目答辩资料转换为Word文档
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn
import os

def add_heading_with_style(doc, text, level=1):
    """添加带样式的标题"""
    heading = doc.add_heading(text, level=level)
    heading.alignment = WD_ALIGN_PARAGRAPH.LEFT
    return heading

def add_question_answer(doc, question, answer):
    """添加问答格式"""
    # 添加问题
    q_para = doc.add_paragraph()
    q_run = q_para.add_run(question)
    q_run.bold = True
    q_run.font.size = Pt(12)
    q_run.font.name = '微软雅黑'
    
    # 添加回答
    a_para = doc.add_paragraph()
    a_run = a_para.add_run(answer)
    a_run.font.size = Pt(11)
    a_run.font.name = '微软雅黑'
    
    # 添加空行
    doc.add_paragraph()

def read_markdown_file(filename):
    """读取markdown文件内容"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"文件 {filename} 不存在")
        return ""
    except Exception as e:
        print(f"读取文件 {filename} 时出错: {e}")
        return ""

def parse_markdown_content(content):
    """解析markdown内容，提取问题和答案"""
    lines = content.split('\n')
    sections = []
    current_section = None
    current_question = None
    current_answer = ""
    
    for line in lines:
        line = line.strip()
        
        # 检测章节标题
        if line.startswith('## '):
            if current_section:
                sections.append(current_section)
            current_section = {
                'title': line[3:],
                'qa_pairs': []
            }
            current_question = None
            current_answer = ""
        
        # 检测问题（以**开头和结尾的行）
        elif line.startswith('**') and line.endswith('**') and '?' in line:
            # 保存上一个问答对
            if current_question and current_answer.strip():
                current_section['qa_pairs'].append({
                    'question': current_question,
                    'answer': current_answer.strip()
                })
            
            current_question = line[2:-2]  # 去掉**标记
            current_answer = ""
        
        # 检测答案（以**回答：**开头）
        elif line.startswith('**回答：**'):
            current_answer = line[6:]  # 去掉**回答：**
        
        # 继续收集答案内容
        elif current_question and line and not line.startswith('#'):
            if current_answer:
                current_answer += " " + line
            else:
                current_answer = line
    
    # 添加最后一个问答对
    if current_section and current_question and current_answer.strip():
        current_section['qa_pairs'].append({
            'question': current_question,
            'answer': current_answer.strip()
        })
    
    # 添加最后一个章节
    if current_section:
        sections.append(current_section)
    
    return sections

def create_word_document():
    """创建Word文档"""
    # 创建新文档
    doc = Document()
    
    # 设置文档标题
    title = doc.add_heading('熵易项目完整答辩手册', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 添加副标题
    subtitle = doc.add_paragraph('——区块链电商平台评委问答全集')
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    subtitle_run = subtitle.runs[0]
    subtitle_run.font.size = Pt(14)
    subtitle_run.font.name = '微软雅黑'
    
    doc.add_page_break()
    
    # 读取主要答辩手册
    main_content = read_markdown_file('熵易项目完整答辩手册.md')
    if main_content:
        sections = parse_markdown_content(main_content)
        
        for section in sections:
            # 添加章节标题
            add_heading_with_style(doc, section['title'], level=1)
            
            # 添加问答对
            for qa in section['qa_pairs']:
                add_question_answer(doc, qa['question'], qa['answer'])
    
    # 添加分页
    doc.add_page_break()
    
    # 读取针对性问题
    targeted_content = read_markdown_file('熵易项目针对性评委提问100题.md')
    if targeted_content:
        add_heading_with_style(doc, '熵易项目针对性评委提问补充', level=1)
        sections = parse_markdown_content(targeted_content)
        
        for section in sections:
            # 添加章节标题
            add_heading_with_style(doc, section['title'], level=2)
            
            # 添加问答对
            for qa in section['qa_pairs']:
                add_question_answer(doc, qa['question'], qa['answer'])
    
    return doc

def main():
    """主函数"""
    print("开始转换答辩资料为Word文档...")
    
    # 创建Word文档
    doc = create_word_document()
    
    # 保存文档
    output_filename = '熵易项目完整答辩手册.docx'
    doc.save(output_filename)
    
    print(f"转换完成！文档已保存为: {output_filename}")
    print(f"文件大小: {os.path.getsize(output_filename) / 1024:.1f} KB")

if __name__ == "__main__":
    main()
