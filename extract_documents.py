#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档内容提取脚本
用于提取PPT和Word文档的内容
"""

import os
import sys
from pathlib import Path

def extract_word_content(file_path):
    """提取Word文档内容"""
    try:
        from docx import Document
        doc = Document(file_path)
        
        content = []
        content.append(f"=== Word文档内容: {file_path} ===\n")
        
        # 提取段落内容
        for i, paragraph in enumerate(doc.paragraphs, 1):
            if paragraph.text.strip():
                content.append(f"段落 {i}: {paragraph.text}")
        
        # 提取表格内容
        for i, table in enumerate(doc.tables, 1):
            content.append(f"\n表格 {i}:")
            for row in table.rows:
                row_text = " | ".join([cell.text.strip() for cell in row.cells])
                if row_text.strip():
                    content.append(f"  {row_text}")
        
        return "\n".join(content)
    
    except ImportError:
        return "错误: 需要安装python-docx库 (pip install python-docx)"
    except Exception as e:
        return f"读取Word文档时出错: {str(e)}"

def extract_ppt_content(file_path):
    """提取PPT内容"""
    try:
        from pptx import Presentation
        prs = Presentation(file_path)
        
        content = []
        content.append(f"=== PPT内容: {file_path} ===\n")
        
        for i, slide in enumerate(prs.slides, 1):
            content.append(f"幻灯片 {i}:")
            
            # 提取文本内容
            for shape in slide.shapes:
                if hasattr(shape, "text") and shape.text.strip():
                    content.append(f"  - {shape.text}")
            
            content.append("")  # 空行分隔
        
        return "\n".join(content)
    
    except ImportError:
        return "错误: 需要安装python-pptx库 (pip install python-pptx)"
    except Exception as e:
        return f"读取PPT文档时出错: {str(e)}"

def main():
    """主函数"""
    current_dir = Path(".")
    
    # 查找PPT和Word文件
    ppt_files = list(current_dir.glob("*.pptx")) + list(current_dir.glob("*.ppt"))
    word_files = list(current_dir.glob("*.docx")) + list(current_dir.glob("*.doc"))
    
    all_content = []
    
    # 提取PPT内容
    for ppt_file in ppt_files:
        print(f"正在提取PPT: {ppt_file}")
        content = extract_ppt_content(ppt_file)
        all_content.append(content)
    
    # 提取Word内容
    for word_file in word_files:
        print(f"正在提取Word: {word_file}")
        content = extract_word_content(word_file)
        all_content.append(content)
    
    # 保存提取的内容
    output_file = "extracted_content.txt"
    with open(output_file, "w", encoding="utf-8") as f:
        f.write("\n\n" + "="*80 + "\n\n".join(all_content))
    
    print(f"内容已提取到: {output_file}")
    
    # 显示提取的内容
    print("\n" + "="*80)
    print("提取的内容:")
    print("="*80)
    for content in all_content:
        print(content)
        print("\n" + "-"*80 + "\n")

if __name__ == "__main__":
    main()
