# 熵易项目评委可能提问的100个问题

## 基础概念与定位类问题 (1-15)

1. 你们的项目名称是"熵易"，为什么选择这个名字？"熵"在你们项目中有什么特殊含义？

2. 你们说这是"DeShop 3.0"，那么1.0和2.0是什么？为什么你们是3.0？

3. 区块链电商平台和传统电商平台的本质区别是什么？

4. 你们如何定义"去中心化电商"？完全去中心化在商业上是否可行？

5. 你们的项目如何体现"互联网+"大赛的主题？

6. 区块链技术在你们项目中起到的核心作用是什么？

7. 你们说要"重构电商信任体系"，现有的信任体系有什么问题？

8. 你们的目标用户群体是谁？B2B、B2C还是C2C？

9. 你们的商业模式是什么？如何盈利？

10. 你们的项目属于哪个赛道？为什么选择这个赛道？

11. 你们的项目现在处于什么阶段？创意、初创还是成长期？

12. 你们团队为什么有能力做这个项目？

13. 你们的项目解决的是真实需求还是伪需求？

14. 你们如何证明市场对这个产品有需求？

15. 你们的项目有哪些创新点？

## 技术实现类问题 (16-35)

16. 你们使用的是哪条区块链？以太坊、BSC还是自建链？

17. 你们说用了"零知识证明"，具体是哪种零知识证明算法？

18. 200+ TPS的处理能力如何实现的？这个数据是如何测试得出的？

19. 你们的智能合约是用什么语言开发的？Solidity还是其他？

20. 跨链支付是如何实现的？支持哪些币种？

21. IPFS存储的成本如何？如何保证数据的持久性？

22. 你们的系统如何处理区块链的扩容问题？

23. Gas费用过高时如何保证用户体验？

24. 你们的共识机制是什么？PoW、PoS还是其他？

25. 如何保证智能合约的安全性？有没有进行安全审计？

26. 你们的系统如何处理区块链分叉问题？

27. NFT溯源系统的技术实现原理是什么？

28. 你们的AI推荐算法是什么？准确率如何？

29. 大数据分析用的什么技术栈？Spark、Flink的具体应用场景？

30. 你们的系统架构能支持多大的并发量？

31. 数据库设计中如何处理区块链数据和传统数据的同步？

32. 你们的加密算法除了AES还用了什么？

33. 如何保证系统的高可用性？

34. 你们的移动端是原生开发还是混合开发？

35. 系统的容灾备份方案是什么？

## 商业模式与市场类问题 (36-55)

36. 你们的收入来源有哪些？手续费、广告费还是其他？

37. 你们如何与淘宝、京东等传统电商平台竞争？

38. 你们的获客成本是多少？如何获取用户？

39. 你们的目标市场规模有多大？

40. 你们如何解决冷启动问题？没有商家和买家怎么办？

41. 你们的平台如何吸引商家入驻？

42. 跨境电商的合规问题如何解决？

43. 你们的定价策略是什么？

44. 如何保证平台的流动性？

45. 你们的商业计划书中的财务预测依据是什么？

46. 你们预计多久能实现盈亏平衡？

47. 你们的融资计划是什么？需要多少资金？

48. 你们如何评估自己的估值？

49. 你们的退出策略是什么？IPO还是被收购？

50. 你们如何应对政策风险？

51. 你们的国际化策略是什么？

52. 你们如何建立品牌知名度？

53. 你们的供应链管理如何实现？

54. 你们如何处理售后服务？

55. 你们的用户留存率预期是多少？

## 竞争与差异化类问题 (56-70)

56. 市场上已经有OpenBazaar等去中心化电商，你们的差异化在哪里？

57. 你们了解币安的NFT市场吗？你们与他们的区别是什么？

58. 传统电商巨头如果也做区块链电商，你们如何应对？

59. 你们的核心竞争力是什么？技术壁垒高吗？

60. 你们如何防止被大公司抄袭？

61. 你们的专利布局如何？

62. 你们团队的技术实力如何证明？

63. 你们如何保持技术领先性？

64. 你们的合作伙伴有哪些？

65. 你们如何建立生态系统？

66. 你们的开发者生态如何建设？

67. 你们如何吸引第三方开发者？

68. 你们的API开放策略是什么？

69. 你们如何与传统金融机构合作？

70. 你们的技术团队规模和结构如何？

## 风险与挑战类问题 (71-85)

71. 区块链技术的监管风险如何应对？

72. 如果政府禁止加密货币交易怎么办？

73. 你们如何应对技术风险？比如智能合约漏洞？

74. 市场接受度不高怎么办？

75. 你们如何应对资金链断裂的风险？

76. 核心团队成员离职怎么办？

77. 技术迭代过快如何跟上？

78. 你们如何应对网络安全攻击？

79. 数据泄露风险如何防范？

80. 你们如何应对市场波动？

81. 竞争对手恶意攻击怎么办？

82. 你们的应急预案是什么？

83. 如何应对用户投诉和纠纷？

84. 你们如何保证平台的公平性？

85. 如何防范洗钱等违法行为？

## 团队与执行类问题 (86-100)

86. 你们团队的分工是怎样的？

87. 团队成员的背景和经验如何？

88. 你们有相关的创业经验吗？

89. 你们的导师或顾问是谁？

90. 你们如何保证团队的稳定性？

91. 你们的股权分配是怎样的？

92. 你们如何激励团队成员？

93. 你们的项目管理方法是什么？

94. 你们如何保证项目按时交付？

95. 你们的学习能力如何？如何快速适应变化？

96. 你们毕业后会继续这个项目吗？

97. 你们如何平衡学业和创业？

98. 你们的长期职业规划是什么？

99. 你们如何看待失败？如果项目失败了怎么办？

100. 你们对这个项目的信心有多大？为什么？

---

## 答辩建议

1. **准备充分的数据支撑**：对于技术指标、市场数据、财务预测等要有详细的计算依据
2. **了解竞品情况**：深入研究同类产品，准备差异化说明
3. **准备demo演示**：确保技术演示流畅，有备用方案
4. **团队协作**：明确每个人的回答领域，避免重复或矛盾
5. **保持谦逊**：承认不足，展示学习能力和改进计划
6. **突出创新点**：重点强调技术创新和商业模式创新
7. **关注社会价值**：强调项目的社会意义和正面影响
