#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新创建干净的Word转换
"""

from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
import os
import re

def setup_document_styles(doc):
    """设置文档样式"""
    styles = doc.styles
    
    # 标题1样式
    heading1_style = styles['Heading 1']
    heading1_style.font.name = '微软雅黑'
    heading1_style.font.size = Pt(16)
    heading1_style.font.color.rgb = RGBColor(0, 51, 102)
    
    # 标题2样式
    heading2_style = styles['Heading 2']
    heading2_style.font.name = '微软雅黑'
    heading2_style.font.size = Pt(14)
    heading2_style.font.color.rgb = RGBColor(0, 102, 204)

def add_cover_page(doc):
    """添加封面页"""
    # 主标题
    main_title = doc.add_heading("熵易项目204题完整答辩资料", 0)
    main_title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    title_run = main_title.runs[0]
    title_run.font.size = Pt(24)
    title_run.font.name = '微软雅黑'
    title_run.font.color.rgb = RGBColor(0, 51, 102)
    
    # 副标题
    sub = doc.add_paragraph()
    sub.alignment = WD_ALIGN_PARAGRAPH.CENTER
    sub_run = sub.add_run("DeShop 3.0 区块链电商平台答辩手册")
    sub_run.font.size = Pt(16)
    sub_run.font.name = '微软雅黑'
    sub_run.font.color.rgb = RGBColor(0, 102, 204)
    
    # 添加空行
    for _ in range(3):
        doc.add_paragraph()
    
    doc.add_page_break()

def add_core_questions(doc):
    """添加核心技术问题"""
    doc.add_heading("核心技术问题 (重点关注)", level=1)
    
    questions = [
        {
            "q": "你们这个平台使用了什么样的技术，做了什么样的创新？",
            "a": "我们使用React.js+Spring Boot+区块链+AI的技术栈。核心创新是零知识证明实现隐私保护、AI驱动的跨链支付优化、NFT全链溯源系统、智能合约自动执行。这些技术让我们实现了200+ TPS处理能力，交易成本降低70%，是传统电商向Web3转型的突破性方案。"
        },
        {
            "q": "区块链用到了那几层？",
            "a": "我们采用5层区块链架构：共识层负责PoS机制和高性能处理，网络层处理P2P通信和跨链桥接，数据层存储智能合约和IPFS数据，应用层实现电商业务逻辑，治理层支持DAO机制和社区决策。这种分层设计保证了系统的可扩展性和稳定性。"
        },
        {
            "q": "特色功能有哪些？",
            "a": "四大特色功能：匿名交易通过零知识证明保护用户隐私，跨链支付用AI优化多币种支付路径降低手续费，商品溯源用NFT技术实现从生产到销售全程可追溯防伪，智能执行通过合约自动处理交易和纠纷。这些功能解决了传统电商的信任、成本、效率三大痛点。"
        },
        {
            "q": "信誉制裁中心怎么实现，有什么样的特点？",
            "a": "基于链上行为数据自动计算信誉分，智能合约执行三级制裁：轻度违规限制交易额度，中度违规冻结保证金，重度违规永久拉黑。特点是去中心化治理、社区投票决策、透明公正执行、激励诚信用户。所有制裁记录不可篡改，形成了可信的商业信誉体系。"
        }
    ]
    
    for i, item in enumerate(questions, 1):
        # 问题
        q_para = doc.add_paragraph()
        q_run = q_para.add_run(f"**{item['q']}**")
        q_run.bold = True
        q_run.font.size = Pt(12)
        q_run.font.name = '微软雅黑'
        q_run.font.color.rgb = RGBColor(0, 51, 102)
        
        # 回答
        a_para = doc.add_paragraph()
        a_label = a_para.add_run("回答：")
        a_label.bold = True
        a_label.font.color.rgb = RGBColor(204, 0, 0)
        a_label.font.name = '微软雅黑'
        
        a_content = a_para.add_run(item['a'])
        a_content.font.size = Pt(11)
        a_content.font.name = '微软雅黑'
        
        doc.add_paragraph()  # 空行

def create_clean_word():
    """创建干净的Word文档"""
    print("🚀 开始创建干净的Word文档...")
    
    # 创建Word文档
    doc = Document()
    setup_document_styles(doc)
    
    # 添加封面
    add_cover_page(doc)
    
    # 添加核心问题
    add_core_questions(doc)
    
    # 添加分隔线
    doc.add_paragraph("---" * 20)
    
    # 添加说明
    note_para = doc.add_paragraph()
    note_run = note_para.add_run("注：由于原文件编码问题，此版本仅包含核心技术问题。")
    note_run.font.size = Pt(10)
    note_run.font.name = '微软雅黑'
    note_run.font.color.rgb = RGBColor(128, 128, 128)
    
    note_para2 = doc.add_paragraph()
    note_run2 = note_para2.add_run("完整的204题内容请参考原始md文件。")
    note_run2.font.size = Pt(10)
    note_run2.font.name = '微软雅黑'
    note_run2.font.color.rgb = RGBColor(128, 128, 128)
    
    # 保存文档
    output_file = "熵易项目核心问题答辩资料_干净版.docx"
    doc.save(output_file)
    
    file_size = os.path.getsize(output_file) / 1024
    print(f"✅ 干净版本创建完成: {output_file} ({file_size:.1f} KB)")
    print("📍 此版本包含最重要的核心技术问题，无乱码问题")

if __name__ == "__main__":
    create_clean_word()
