#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建完整的熵易项目答辩Word文档
包含所有答辩资料的整合版本
"""

from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
import os

def setup_document_styles(doc):
    """设置文档样式"""
    # 设置正文样式
    styles = doc.styles
    
    # 标题1样式
    heading1_style = styles['Heading 1']
    heading1_style.font.name = '微软雅黑'
    heading1_style.font.size = Pt(16)
    heading1_style.font.color.rgb = RGBColor(0, 51, 102)
    
    # 标题2样式
    heading2_style = styles['Heading 2']
    heading2_style.font.name = '微软雅黑'
    heading2_style.font.size = Pt(14)
    heading2_style.font.color.rgb = RGBColor(0, 102, 204)

def add_cover_page(doc):
    """添加封面页"""
    # 主标题
    title = doc.add_heading('熵易项目', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    title_run = title.runs[0]
    title_run.font.size = Pt(28)
    title_run.font.name = '微软雅黑'
    title_run.font.color.rgb = RGBColor(0, 51, 102)
    
    # 副标题
    subtitle = doc.add_heading('完整答辩手册', level=1)
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    subtitle_run = subtitle.runs[0]
    subtitle_run.font.size = Pt(20)
    subtitle_run.font.name = '微软雅黑'
    subtitle_run.font.color.rgb = RGBColor(0, 102, 204)
    
    # 项目描述
    desc = doc.add_paragraph()
    desc.alignment = WD_ALIGN_PARAGRAPH.CENTER
    desc_run = desc.add_run('DeShop 3.0 智链重构电商平台领先者')
    desc_run.font.size = Pt(16)
    desc_run.font.name = '微软雅黑'
    desc_run.font.color.rgb = RGBColor(102, 102, 102)
    
    # 添加空行
    for _ in range(5):
        doc.add_paragraph()
    
    # 项目特色
    features = [
        "🔐 零知识证明保护隐私",
        "⚡ 200+ TPS高性能处理",
        "🌐 AI驱动跨链支付",
        "🔗 全链路NFT溯源",
        "💰 降低交易成本70%"
    ]
    
    for feature in features:
        para = doc.add_paragraph()
        para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        run = para.add_run(feature)
        run.font.size = Pt(14)
        run.font.name = '微软雅黑'
    
    doc.add_page_break()

def add_toc(doc):
    """添加目录"""
    toc_heading = doc.add_heading('目录', level=1)
    toc_heading.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    toc_items = [
        "1. 项目概述与核心价值",
        "2. 技术架构与实现方案", 
        "3. 功能模块与用户体验",
        "4. 测试数据与性能指标",
        "5. 商业模式与市场价值",
        "6. 未来发展与战略规划",
        "7. 基础概念与定位问题",
        "8. 技术实现类问题",
        "9. 风险应对与挑战",
        "10. 附录：关键数据汇总"
    ]
    
    for item in toc_items:
        para = doc.add_paragraph(item)
        para.style = 'List Number'
    
    doc.add_page_break()

def read_and_parse_file(filename):
    """读取并解析markdown文件"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        sections = []
        lines = content.split('\n')
        current_section = None
        current_content = []
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('## '):
                if current_section:
                    current_section['content'] = '\n'.join(current_content)
                    sections.append(current_section)
                
                current_section = {
                    'title': line[3:],
                    'content': ''
                }
                current_content = []
            elif line.startswith('# '):
                continue  # 跳过主标题
            else:
                current_content.append(line)
        
        if current_section:
            current_section['content'] = '\n'.join(current_content)
            sections.append(current_section)
        
        return sections
    
    except Exception as e:
        print(f"读取文件 {filename} 时出错: {e}")
        return []

def add_qa_content(doc, content):
    """添加问答内容"""
    lines = content.split('\n')
    current_question = ""
    current_answer = ""
    in_answer = False
    
    for line in lines:
        line = line.strip()
        
        if line.startswith('**') and line.endswith('**') and '?' in line:
            # 保存上一个问答对
            if current_question and current_answer:
                add_single_qa(doc, current_question, current_answer)
            
            current_question = line[2:-2]
            current_answer = ""
            in_answer = False
        
        elif line.startswith('**回答：**'):
            current_answer = line[6:]
            in_answer = True
        
        elif in_answer and line and not line.startswith('#'):
            if current_answer:
                current_answer += " " + line
            else:
                current_answer = line
    
    # 添加最后一个问答对
    if current_question and current_answer:
        add_single_qa(doc, current_question, current_answer)

def add_single_qa(doc, question, answer):
    """添加单个问答对"""
    # 问题
    q_para = doc.add_paragraph()
    q_run = q_para.add_run(f"Q: {question}")
    q_run.bold = True
    q_run.font.size = Pt(12)
    q_run.font.name = '微软雅黑'
    q_run.font.color.rgb = RGBColor(0, 51, 102)
    
    # 答案
    a_para = doc.add_paragraph()
    a_run = a_para.add_run(f"A: {answer}")
    a_run.font.size = Pt(11)
    a_run.font.name = '微软雅黑'
    
    # 分隔线
    doc.add_paragraph("─" * 50)

def create_complete_document():
    """创建完整文档"""
    doc = Document()
    setup_document_styles(doc)
    
    # 添加封面
    add_cover_page(doc)
    
    # 添加目录
    add_toc(doc)
    
    # 处理主要答辩手册
    print("处理主要答辩手册...")
    main_sections = read_and_parse_file('熵易项目完整答辩手册.md')
    for section in main_sections:
        doc.add_heading(section['title'], level=1)
        add_qa_content(doc, section['content'])
        doc.add_page_break()
    
    # 处理针对性问题
    print("处理针对性问题...")
    targeted_sections = read_and_parse_file('熵易项目针对性评委提问100题.md')
    for section in targeted_sections:
        doc.add_heading(f"补充：{section['title']}", level=1)
        add_qa_content(doc, section['content'])
        doc.add_page_break()
    
    # 添加附录
    doc.add_heading('附录：关键数据汇总', level=1)
    
    key_data = [
        "📊 性能指标：200+ TPS处理能力，3秒响应时间",
        "🔒 安全指标：100% SQL注入防护，零安全事故",
        "💰 成本优势：交易成本降低70-80%",
        "📈 商业数据：月交易额100万元，用户留存率65%",
        "🎯 发展目标：3年内用户数200万，交易额200亿元",
        "💡 技术专利：3项发明专利，2项软件著作权",
        "🌍 市场规模：全球电商5.7万亿美元，区块链电商500亿美元"
    ]
    
    for data in key_data:
        para = doc.add_paragraph(data)
        para.style = 'List Bullet'
    
    return doc

def main():
    """主函数"""
    print("开始创建完整的答辩Word文档...")
    
    doc = create_complete_document()
    
    # 保存文档
    output_filename = '熵易项目完整答辩资料.docx'
    doc.save(output_filename)
    
    print(f"✅ 完整答辩文档创建成功！")
    print(f"📄 文件名：{output_filename}")
    print(f"📦 文件大小：{os.path.getsize(output_filename) / 1024:.1f} KB")
    print(f"📍 保存位置：{os.path.abspath(output_filename)}")

if __name__ == "__main__":
    main()
