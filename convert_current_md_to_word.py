#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将当前的两个md文件转换为Word文档
"""

from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
import os
import re

def setup_document_styles(doc):
    """设置文档样式"""
    styles = doc.styles
    
    # 标题1样式
    heading1_style = styles['Heading 1']
    heading1_style.font.name = '微软雅黑'
    heading1_style.font.size = Pt(16)
    heading1_style.font.color.rgb = RGBColor(0, 51, 102)
    
    # 标题2样式
    heading2_style = styles['Heading 2']
    heading2_style.font.name = '微软雅黑'
    heading2_style.font.size = Pt(14)
    heading2_style.font.color.rgb = RGBColor(0, 102, 204)

def add_cover_page(doc, title, subtitle=""):
    """添加封面页"""
    # 主标题
    main_title = doc.add_heading(title, 0)
    main_title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    title_run = main_title.runs[0]
    title_run.font.size = Pt(24)
    title_run.font.name = '微软雅黑'
    title_run.font.color.rgb = RGBColor(0, 51, 102)
    
    if subtitle:
        # 副标题
        sub = doc.add_paragraph()
        sub.alignment = WD_ALIGN_PARAGRAPH.CENTER
        sub_run = sub.add_run(subtitle)
        sub_run.font.size = Pt(16)
        sub_run.font.name = '微软雅黑'
        sub_run.font.color.rgb = RGBColor(0, 102, 204)
    
    # 添加空行
    for _ in range(3):
        doc.add_paragraph()
    
    doc.add_page_break()

def parse_markdown_to_word(doc, content):
    """将markdown内容转换为Word格式"""
    lines = content.split('\n')
    
    for line in lines:
        line = line.strip()
        
        if not line:
            doc.add_paragraph()
            continue
        
        # 处理标题
        if line.startswith('# '):
            doc.add_heading(line[2:], level=1)
        elif line.startswith('## '):
            doc.add_heading(line[3:], level=2)
        elif line.startswith('### '):
            doc.add_heading(line[4:], level=3)
        
        # 处理粗体问题
        elif line.startswith('**') and line.endswith('**'):
            para = doc.add_paragraph()
            run = para.add_run(line[2:-2])
            run.bold = True
            run.font.size = Pt(12)
            run.font.name = '微软雅黑'
            run.font.color.rgb = RGBColor(0, 51, 102)
        
        # 处理回答
        elif line.startswith('**回答：**'):
            para = doc.add_paragraph()
            # 添加"回答："标签
            answer_label = para.add_run("回答：")
            answer_label.bold = True
            answer_label.font.color.rgb = RGBColor(204, 0, 0)
            # 添加回答内容
            answer_content = para.add_run(line[6:])
            answer_content.font.size = Pt(11)
            answer_content.font.name = '微软雅黑'
        
        # 处理列表项
        elif line.startswith('- ') or line.startswith('* '):
            para = doc.add_paragraph(line[2:], style='List Bullet')
            para.style.font.name = '微软雅黑'
        elif re.match(r'^\d+\. ', line):
            para = doc.add_paragraph(line[3:], style='List Number')
            para.style.font.name = '微软雅黑'
        
        # 处理普通段落
        else:
            para = doc.add_paragraph(line)
            para.style.font.name = '微软雅黑'
            para.style.font.size = Pt(11)

def read_markdown_file(filename):
    """读取markdown文件"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"❌ 文件 {filename} 不存在")
        return None
    except Exception as e:
        print(f"❌ 读取文件 {filename} 时出错: {e}")
        return None

def convert_file_to_word(input_file, output_file):
    """将单个md文件转换为Word"""
    print(f"📄 正在转换: {input_file}")
    
    content = read_markdown_file(input_file)
    if content is None:
        return False
    
    # 创建Word文档
    doc = Document()
    setup_document_styles(doc)
    
    # 根据文件名确定标题
    if "完整答辩手册" in input_file:
        title = "熵易项目完整答辩手册"
        subtitle = "DeShop 3.0 区块链电商平台"
    elif "针对性评委提问" in input_file:
        title = "熵易项目针对性评委提问"
        subtitle = "100题专项问答集"
    else:
        title = input_file.replace('.md', '')
        subtitle = ""
    
    # 添加封面
    add_cover_page(doc, title, subtitle)
    
    # 转换内容
    parse_markdown_to_word(doc, content)
    
    # 保存文档
    doc.save(output_file)
    
    file_size = os.path.getsize(output_file) / 1024
    print(f"✅ 转换完成: {output_file} ({file_size:.1f} KB)")
    return True

def main():
    """主函数"""
    print("🚀 开始转换md文件为Word文档...")
    
    # 要转换的文件列表
    files_to_convert = [
        ("熵易项目完整答辩手册.md", "熵易项目完整答辩手册.docx"),
        ("熵易项目针对性评委提问100题.md", "熵易项目针对性评委提问100题.docx")
    ]
    
    success_count = 0
    
    for input_file, output_file in files_to_convert:
        if convert_file_to_word(input_file, output_file):
            success_count += 1
    
    print(f"\n📊 转换结果:")
    print(f"✅ 成功转换: {success_count} 个文件")
    print(f"❌ 转换失败: {len(files_to_convert) - success_count} 个文件")
    
    if success_count > 0:
        print(f"\n📍 文件保存位置: {os.getcwd()}")
        print("🎉 所有Word文档已准备就绪，可用于答辩！")

if __name__ == "__main__":
    main()
